/**
 * Local Environment Setup Script
 * Sets up the local development environment for ATMA Backend
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkCommand(command) {
  try {
    execSync(`${command} --version`, { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

function checkService(serviceName, port) {
  return new Promise((resolve) => {
    const { spawn } = require('child_process');
    const process = spawn('netstat', ['-an'], { stdio: 'pipe' });
    
    let output = '';
    process.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    process.on('close', () => {
      const isRunning = output.includes(`:${port}`);
      resolve(isRunning);
    });
    
    process.on('error', () => {
      resolve(false);
    });
  });
}

async function checkPrerequisites() {
  log('\n🔍 Checking prerequisites...', colors.cyan);
  
  const checks = [
    { name: 'Node.js', command: 'node', required: true },
    { name: 'npm', command: 'npm', required: true },
    { name: 'PostgreSQL', command: 'psql', required: true },
    { name: 'Git', command: 'git', required: false }
  ];
  
  let allGood = true;
  
  for (const check of checks) {
    const available = checkCommand(check.command);
    if (available) {
      log(`✅ ${check.name} is available`, colors.green);
    } else {
      if (check.required) {
        log(`❌ ${check.name} is required but not found`, colors.red);
        allGood = false;
      } else {
        log(`⚠️  ${check.name} is not available (optional)`, colors.yellow);
      }
    }
  }
  
  // Check for RabbitMQ
  const rabbitmqRunning = await checkService('RabbitMQ', 5672);
  if (rabbitmqRunning) {
    log('✅ RabbitMQ is running', colors.green);
  } else {
    log('❌ RabbitMQ is not running on port 5672', colors.red);
    log('   Please install and start RabbitMQ:', colors.yellow);
    log('   - Windows: choco install rabbitmq', colors.yellow);
    log('   - macOS: brew install rabbitmq', colors.yellow);
    log('   - Linux: sudo apt-get install rabbitmq-server', colors.yellow);
    allGood = false;
  }
  
  // Check for PostgreSQL
  const postgresRunning = await checkService('PostgreSQL', 5432);
  if (postgresRunning) {
    log('✅ PostgreSQL is running', colors.green);
  } else {
    log('❌ PostgreSQL is not running on port 5432', colors.red);
    allGood = false;
  }
  
  return allGood;
}

function installDependencies() {
  log('\n📦 Installing dependencies for all services...', colors.cyan);
  
  const services = [
    'api-gateway',
    'auth-service',
    'assessment-service',
    'analysis-worker',
    'archive-service'
  ];
  
  for (const service of services) {
    const servicePath = path.join(__dirname, '..', service);
    
    if (!fs.existsSync(servicePath)) {
      log(`⚠️  Service directory not found: ${service}`, colors.yellow);
      continue;
    }
    
    log(`📦 Installing dependencies for ${service}...`, colors.blue);
    
    try {
      execSync('npm install', {
        cwd: servicePath,
        stdio: 'inherit'
      });
      log(`✅ Dependencies installed for ${service}`, colors.green);
    } catch (error) {
      log(`❌ Failed to install dependencies for ${service}`, colors.red);
      throw error;
    }
  }
}

function setupDatabase() {
  log('\n🗄️  Setting up database...', colors.cyan);
  
  try {
    // Check if database exists
    log('📋 Checking database connection...', colors.blue);
    
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'atma_db',
      user: process.env.DB_USER || 'atma_user',
      password: process.env.DB_PASSWORD || 'secret-passworrd'
    };
    
    log('💡 Database setup instructions:', colors.yellow);
    log('   1. Connect to PostgreSQL as superuser:', colors.yellow);
    log(`      psql -U postgres`, colors.yellow);
    log('   2. Run the following commands:', colors.yellow);
    log(`      CREATE DATABASE ${dbConfig.database};`, colors.yellow);
    log(`      CREATE USER ${dbConfig.user} WITH PASSWORD '${dbConfig.password}';`, colors.yellow);
    log(`      GRANT ALL PRIVILEGES ON DATABASE ${dbConfig.database} TO ${dbConfig.user};`, colors.yellow);
    log(`      \\c ${dbConfig.database}`, colors.yellow);
    log(`      \\i scripts/init-databases.sql`, colors.yellow);
    
    log('✅ Database setup instructions provided', colors.green);
    
  } catch (error) {
    log(`❌ Database setup failed: ${error.message}`, colors.red);
    throw error;
  }
}

function createStartScript() {
  log('\n📝 Creating start script...', colors.cyan);
  
  const startScript = `#!/bin/bash
# Start all ATMA Backend services locally

echo "🚀 Starting ATMA Backend Services..."

# Start services in background
cd api-gateway && npm start &
cd ../auth-service && npm start &
cd ../assessment-service && npm start &
cd ../analysis-worker && npm start &
cd ../archive-service && npm start &

echo "✅ All services started!"
echo "📋 Service URLs:"
echo "   🌐 API Gateway:        http://localhost:3000"
echo "   🔐 Auth Service:       http://localhost:3001"
echo "   📁 Archive Service:    http://localhost:3002"
echo "   📝 Assessment Service: http://localhost:3003"
echo "   ⚙️  Analysis Worker:    (background process)"
echo ""
echo "💡 Press Ctrl+C to stop all services"

# Wait for user input
read -p "Press Enter to stop all services..."

# Kill all node processes (be careful with this!)
pkill -f "node.*start"
echo "🛑 All services stopped"
`;
  
  const scriptPath = path.join(__dirname, 'start-all-local.sh');
  fs.writeFileSync(scriptPath, startScript);
  
  // Make script executable (Unix/Linux/macOS)
  try {
    execSync(`chmod +x ${scriptPath}`);
  } catch (error) {
    // Ignore on Windows
  }
  
  log(`✅ Start script created: ${scriptPath}`, colors.green);
}

function showUsageInstructions() {
  log('\n📖 Usage Instructions', colors.cyan);
  log('='.repeat(50), colors.cyan);
  
  log('\n1. Start all services:', colors.bright);
  log('   node scripts/start-local-services.js', colors.yellow);
  
  log('\n2. Run end-to-end test:', colors.bright);
  log('   node scripts/test-local-flow.js', colors.yellow);
  
  log('\n3. Individual service URLs:', colors.bright);
  log('   🌐 API Gateway:        http://localhost:3000', colors.green);
  log('   🔐 Auth Service:       http://localhost:3001', colors.green);
  log('   📁 Archive Service:    http://localhost:3002', colors.green);
  log('   📝 Assessment Service: http://localhost:3003', colors.green);
  
  log('\n4. Database connection:', colors.bright);
  log('   Host: localhost:5432', colors.green);
  log('   Database: atma_db', colors.green);
  log('   User: atma_user', colors.green);
  
  log('\n5. RabbitMQ Management:', colors.bright);
  log('   URL: http://localhost:15672', colors.green);
  log('   User: guest / guest', colors.green);
}

async function main() {
  log('🎯 ATMA Backend Local Environment Setup', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  try {
    // Load environment variables
    require('dotenv').config({ path: path.join(__dirname, '..', '.env.shared') });
    
    // Check prerequisites
    const prerequisitesOk = await checkPrerequisites();
    if (!prerequisitesOk) {
      log('\n❌ Prerequisites check failed. Please install missing components.', colors.red);
      process.exit(1);
    }
    
    // Install dependencies
    installDependencies();
    
    // Setup database
    setupDatabase();
    
    // Create start script
    createStartScript();
    
    // Show usage instructions
    showUsageInstructions();
    
    log('\n✅ Local environment setup completed!', colors.green);
    log('🚀 You can now start the services using the instructions above.', colors.bright);
    
  } catch (error) {
    log(`\n❌ Setup failed: ${error.message}`, colors.red);
    process.exit(1);
  }
}

// Run setup
main();
