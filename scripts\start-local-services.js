/**
 * Local Services Starter
 * Starts all microservices in local development environment
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.shared') });

// Service configurations
const services = [
  {
    name: 'auth-service',
    path: path.join(__dirname, '..', 'auth-service'),
    port: 3001,
    command: 'npm',
    args: ['start'],
    env: {
      ...process.env,
      PORT: '3001',
      NODE_ENV: 'development'
    }
  },
  {
    name: 'archive-service',
    path: path.join(__dirname, '..', 'archive-service'),
    port: 3002,
    command: 'npm',
    args: ['start'],
    env: {
      ...process.env,
      PORT: '3002',
      NODE_ENV: 'development'
    }
  },
  {
    name: 'assessment-service',
    path: path.join(__dirname, '..', 'assessment-service'),
    port: 3003,
    command: 'npm',
    args: ['start'],
    env: {
      ...process.env,
      PORT: '3003',
      NODE_ENV: 'development',
      AUTH_SERVICE_URL: 'http://localhost:3001'
    }
  },
  {
    name: 'analysis-worker',
    path: path.join(__dirname, '..', 'analysis-worker'),
    port: null, // Worker doesn't expose HTTP port
    command: 'npm',
    args: ['start'],
    env: {
      ...process.env,
      NODE_ENV: 'development',
      ARCHIVE_SERVICE_URL: 'http://localhost:3002'
    }
  },
  {
    name: 'api-gateway',
    path: path.join(__dirname, '..', 'api-gateway'),
    port: 3000,
    command: 'npm',
    args: ['start'],
    env: {
      ...process.env,
      PORT: '3000',
      NODE_ENV: 'development',
      AUTH_SERVICE_URL: 'http://localhost:3001',
      ASSESSMENT_SERVICE_URL: 'http://localhost:3003',
      ARCHIVE_SERVICE_URL: 'http://localhost:3002'
    }
  }
];

// Store running processes
const runningProcesses = [];

// Cleanup function
function cleanup() {
  console.log('\n🛑 Shutting down all services...');
  
  runningProcesses.forEach(({ name, process }) => {
    console.log(`   Stopping ${name}...`);
    process.kill('SIGTERM');
  });
  
  setTimeout(() => {
    process.exit(0);
  }, 2000);
}

// Handle process termination
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Function to start a service
function startService(service) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Starting ${service.name}...`);
    
    // Check if service directory exists
    if (!fs.existsSync(service.path)) {
      console.error(`❌ Service directory not found: ${service.path}`);
      reject(new Error(`Service directory not found: ${service.path}`));
      return;
    }
    
    // Check if package.json exists
    const packageJsonPath = path.join(service.path, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      console.error(`❌ package.json not found for ${service.name}`);
      reject(new Error(`package.json not found for ${service.name}`));
      return;
    }
    
    // Spawn the process
    const childProcess = spawn(service.command, service.args, {
      cwd: service.path,
      env: service.env,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // Store the process
    runningProcesses.push({
      name: service.name,
      process: childProcess
    });
    
    // Handle stdout
    childProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[${service.name}] ${output}`);
      }
    });
    
    // Handle stderr
    childProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.error(`[${service.name}] ERROR: ${output}`);
      }
    });
    
    // Handle process exit
    childProcess.on('exit', (code, signal) => {
      if (code !== 0 && signal !== 'SIGTERM') {
        console.error(`❌ ${service.name} exited with code ${code}`);
      } else {
        console.log(`✅ ${service.name} stopped`);
      }
    });
    
    // Handle process error
    childProcess.on('error', (error) => {
      console.error(`❌ Failed to start ${service.name}:`, error.message);
      reject(error);
    });
    
    // Wait a bit for the service to start
    setTimeout(() => {
      console.log(`✅ ${service.name} started`);
      resolve();
    }, 2000);
  });
}

// Main function
async function startAllServices() {
  console.log('🎯 Starting ATMA Backend Services in Local Environment');
  console.log('============================================================');
  
  try {
    // Start services sequentially to avoid port conflicts
    for (const service of services) {
      await startService(service);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between services
    }
    
    console.log('\n============================================================');
    console.log('✅ All services started successfully!');
    console.log('\n📋 Service URLs:');
    console.log('   🌐 API Gateway:        http://localhost:3000');
    console.log('   🔐 Auth Service:       http://localhost:3001');
    console.log('   📁 Archive Service:    http://localhost:3002');
    console.log('   📝 Assessment Service: http://localhost:3003');
    console.log('   ⚙️  Analysis Worker:    (background process)');
    console.log('\n💡 Press Ctrl+C to stop all services');
    
    // Keep the process running
    setInterval(() => {
      // Check if all processes are still running
      const runningCount = runningProcesses.filter(({ process }) => !process.killed).length;
      if (runningCount === 0) {
        console.log('❌ All services have stopped');
        process.exit(1);
      }
    }, 5000);
    
  } catch (error) {
    console.error('❌ Failed to start services:', error.message);
    cleanup();
  }
}

// Start all services
startAllServices();
