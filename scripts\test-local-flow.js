/**
 * Local Flow Testing Script
 * Tests the complete flow in local development environment
 *
 * Prerequisites:
 * - PostgreSQL running on localhost:5432
 * - RabbitMQ running on localhost:5672
 * - All services running locally (use start-local-services.js)
 *
 * Usage: node scripts/test-local-flow.js
 */

require('dotenv').config({ path: '.env.shared' });

const axios = require('axios');
const { Client } = require('pg');

// Configuration for local environment
const config = {
  apiGateway: 'http://localhost:3000',
  authService: 'http://localhost:3001',
  assessmentService: 'http://localhost:3003',
  archiveService: 'http://localhost:3002',
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'atma_db',
    user: process.env.DB_USER || 'atma_user',
    password: process.env.DB_PASSWORD || 'secret-passworrd'
  },
  timeout: 60000 // 1 minute timeout for analysis
};

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User'
};

// Assessment data
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 55,
    perseverance: 68,
    honesty: 73,
    zest: 66,
    love: 80,
    kindness: 75,
    socialIntelligence: 65,
    teamwork: 60,
    fairness: 70,
    leadership: 67,
    forgiveness: 58,
    humility: 62,
    prudence: 69,
    selfRegulation: 61,
    appreciationOfBeauty: 50,
    gratitude: 72,
    hope: 77,
    humor: 65,
    spirituality: 55
  },
  multipleIntelligences: {
    linguistic: 85,
    logicalMathematical: 90,
    spatial: 75,
    bodilyKinesthetic: 60,
    musical: 55,
    interpersonal: 70,
    intrapersonal: 65,
    naturalistic: 50
  },
  cognitiveStyleIndex: {
    analytic: 80,
    intuitive: 60
  }
};

// Database client
let dbClient;
let authToken;

async function connectToDatabase() {
  console.log('🔌 Connecting to database...');
  dbClient = new Client(config.database);
  await dbClient.connect();
  console.log('✅ Database connected');
}

async function disconnectFromDatabase() {
  if (dbClient) {
    await dbClient.end();
    console.log('🔌 Database connection closed');
  }
}

async function testServiceHealth() {
  console.log('\n🏥 Testing service health...');
  
  const services = [
    { name: 'API Gateway', url: `${config.apiGateway}/health` },
    { name: 'Auth Service', url: `${config.authService}/health` },
    { name: 'Assessment Service', url: `${config.assessmentService}/health` },
    { name: 'Archive Service', url: `${config.archiveService}/health` }
  ];
  
  for (const service of services) {
    try {
      const response = await axios.get(service.url);
      console.log(`✅ ${service.name} is healthy:`, response.data.status || 'OK');
    } catch (error) {
      console.error(`❌ ${service.name} health check failed:`, error.message);
      throw error;
    }
  }
}

async function registerUser() {
  console.log('\n👤 Registering test user...');
  
  try {
    const response = await axios.post(
      `${config.apiGateway}/auth/register`,
      testUser,
      {
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    console.log('✅ User registered successfully');
    return response.data;
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('ℹ️ User already exists, proceeding with login');
      return null;
    }
    console.error('❌ User registration failed:', error.response?.data || error.message);
    throw error;
  }
}

async function loginUser() {
  console.log('\n🔐 Logging in user...');
  
  try {
    const response = await axios.post(
      `${config.apiGateway}/auth/login`,
      {
        email: testUser.email,
        password: testUser.password
      },
      {
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    authToken = response.data.data.token;
    console.log('✅ User logged in successfully');
    console.log('🎫 Token received');
    
    return response.data;
  } catch (error) {
    console.error('❌ User login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function submitAssessment() {
  console.log('\n📝 Submitting assessment...');
  
  try {
    const response = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      assessmentData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      }
    );
    
    console.log('✅ Assessment submitted successfully');
    console.log('📄 Response:', response.data);
    
    return response.data.data.analysisId;
  } catch (error) {
    console.error('❌ Assessment submission failed:', error.response?.data || error.message);
    throw error;
  }
}

async function waitForAnalysis(analysisId) {
  console.log('\n⏳ Waiting for analysis to complete...');
  
  const startTime = Date.now();
  const maxWaitTime = config.timeout;
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      // Check database for completed analysis
      const result = await dbClient.query(
        'SELECT * FROM archive.analysis_results WHERE id = $1',
        [analysisId]
      );
      
      if (result.rows.length > 0) {
        const analysis = result.rows[0];
        console.log('✅ Analysis completed!');
        console.log('📊 Analysis result:', {
          id: analysis.id,
          userId: analysis.user_id,
          status: analysis.status,
          createdAt: analysis.created_at,
          hasPersonaProfile: !!analysis.persona_profile
        });
        
        return analysis;
      }
      
      console.log('⏳ Analysis still processing... waiting 5 seconds');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
    } catch (error) {
      console.error('❌ Error checking analysis status:', error.message);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  throw new Error('Analysis timeout - analysis did not complete within expected time');
}

async function retrieveResults(analysisId) {
  console.log('\n📚 Retrieving analysis results...');
  
  try {
    const response = await axios.get(
      `${config.apiGateway}/archive/results/${analysisId}`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      }
    );
    
    console.log('✅ Results retrieved successfully');
    console.log('📄 Retrieved data:', {
      id: response.data.data.id,
      userId: response.data.data.userId,
      status: response.data.data.status,
      hasAssessmentData: !!response.data.data.assessmentData,
      hasPersonaProfile: !!response.data.data.personaProfile
    });
    
    return response.data;
  } catch (error) {
    console.error('❌ Results retrieval failed:', error.response?.data || error.message);
    throw error;
  }
}

async function runTest() {
  console.log('🚀 Starting Local Flow Test');
  console.log('============================================================');
  
  try {
    // Connect to database
    await connectToDatabase();
    
    // Test service health
    await testServiceHealth();
    
    // Register user (if needed)
    await registerUser();
    
    // Login user
    await loginUser();
    
    // Submit assessment
    const analysisId = await submitAssessment();
    
    // Wait for analysis to complete
    await waitForAnalysis(analysisId);
    
    // Retrieve results
    await retrieveResults(analysisId);
    
    console.log('\n============================================================');
    console.log('✅ Local Flow Test PASSED');
    console.log('🎉 Complete end-to-end flow working correctly!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n============================================================');
    console.log('❌ Local Flow Test FAILED');
    process.exit(1);
  } finally {
    await disconnectFromDatabase();
  }
}

// Run the test
runTest();
