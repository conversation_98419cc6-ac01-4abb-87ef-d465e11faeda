{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"error":"password authentication failed for user \"postgres\"","level":"error","message":"Health check failed","service":"auth-service","status":"unhealthy","timestamp":"2025-07-15 17:28:18"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:28"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"email":"<EMAIL>","error":"password authentication failed for user \"postgres\"","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"password authentication failed for user \"postgres\"","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd0a84f-2c03-4720-8c3b-f53fc6426413","service":"auth-service","stack":"SequelizeConnectionError: password authentication failed for user \"postgres\"\n    at Client._connectionCallback (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n    at Client._handleErrorWhileConnecting (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:336:19)\n    at Client._handleErrorMessage (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:356:19)\n    at Connection.emit (node:events:518:28)\n    at D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\connection.js:116:12\n    at Parser.parse (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n    at Socket.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-07-15 17:33:27","userAgent":"axios/1.10.0"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:19"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5a42651-a7c9-452a-bc44-f3254f28bbec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:41"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd5dc5d1-b2c6-41b0-9714-ab5590cac3fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:10:34"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e6432726-eb9a-4f2c-b73b-fb407665aa5c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:11:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bbebde79-15d3-4866-9c4f-f6d844bce977","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:02"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"91f4529c-1757-4be0-bbaf-aa837095bd42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:51"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"92ca9ade-1823-4b90-9b3d-9dca38f46107","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:14:45"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0aeb1e9d-7a9b-442b-8295-502a04eaa0fd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:16:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c9239e33-6180-4569-8e83-7a9ec1c2b6f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:20:58"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d0a44d01-d8de-491a-9b62-55017c0da149","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:27:52"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"612c9f01-812d-4b57-b089-5e71452dd96a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
