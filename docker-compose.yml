version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    container_name: atma-postgres
    environment:
      POSTGRES_DB: atma_db
      POSTGRES_USER: atma_user
      POSTGRES_PASSWORD: secret-passworrd
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - atma-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: atma-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - atma-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Application Services
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: atma-api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=your_jwt_secret_key_change_in_production
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
    depends_on:
      - auth-service
      - archive-service
      - assessment-service
    networks:
      - atma-network
    volumes:
      - ./api-gateway/logs:/app/logs

  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: atma-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=atma_user
      - DB_PASSWORD=secret-passworrd
      - DB_SCHEMA=auth
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - JWT_EXPIRES_IN=7d
      - BCRYPT_ROUNDS=12
      - DEFAULT_TOKEN_BALANCE=5
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./auth-service/logs:/app/logs

  archive-service:
    build:
      context: ./archive-service
      dockerfile: Dockerfile
    container_name: atma-archive-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=atma_user
      - DB_PASSWORD=secret-passworrd
      - DB_SCHEMA=archive
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - AUTH_SERVICE_URL=http://auth-service:3001
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - atma-network
    volumes:
      - ./archive-service/logs:/app/logs

  assessment-service:
    build:
      context: ./assessment-service
      dockerfile: Dockerfile
    container_name: atma-assessment-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - QUEUE_DURABLE=true
      - MESSAGE_PERSISTENT=true
      - AUTH_SERVICE_URL=http://auth-service:3001
      - AUTH_SERVICE_KEY=internal_service_secret_key_change_in_production
      - ANALYSIS_TOKEN_COST=1
    depends_on:
      rabbitmq:
        condition: service_healthy
      auth-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./assessment-service/logs:/app/logs

  analysis-worker:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=development
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - QUEUE_DURABLE=true
      - MESSAGE_PERSISTENT=true
      - GOOGLE_AI_API_KEY=AIzaSyCgnveml1MCF6GIWq7rchtYwxA8wrQsYcc
      - GOOGLE_AI_MODEL=gemini-1.5-pro
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production
      - WORKER_CONCURRENCY=3
      - MAX_RETRIES=3
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_started
    networks:
      - atma-network
    volumes:
      - ./analysis-worker/logs:/app/logs
    deploy:
      replicas: 1  # Run 1 worker instance for testing

volumes:
  postgres_data:
    driver: local
  rabbitmq_data:
    driver: local

networks:
  atma-network:
    driver: bridge
