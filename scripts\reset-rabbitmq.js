/**
 * Reset RabbitMQ Configuration Script
 * Cleans up and recreates RabbitMQ exchanges and queues
 */

const amqp = require('../analysis-worker/node_modules/amqplib');

const RABBITMQ_URL = 'amqp://guest:guest@localhost:5672';
const EXCHANGE_NAME = 'atma_exchange';
const QUEUE_NAME = 'assessment_analysis';

async function resetRabbitMQ() {
  let connection;
  let channel;

  try {
    console.log('🔌 Connecting to RabbitMQ...');
    connection = await amqp.connect(RABBITMQ_URL);
    channel = await connection.createChannel();

    console.log('🧹 Cleaning up existing configuration...');
    
    // Delete queue if exists
    try {
      await channel.deleteQueue(QUEUE_NAME);
      console.log(`✅ Deleted queue: ${QUEUE_NAME}`);
    } catch (error) {
      console.log(`ℹ️  Queue ${QUEUE_NAME} doesn't exist or already deleted`);
    }

    // Delete exchange if exists
    try {
      await channel.deleteExchange(EXCHANGE_NAME);
      console.log(`✅ Deleted exchange: ${EXCHANGE_NAME}`);
    } catch (error) {
      console.log(`ℹ️  Exchange ${EXCHANGE_NAME} doesn't exist or already deleted`);
    }

    console.log('🏗️  Creating new configuration...');

    // Create exchange
    await channel.assertExchange(EXCHANGE_NAME, 'direct', {
      durable: true
    });
    console.log(`✅ Created exchange: ${EXCHANGE_NAME}`);

    // Create queue with proper configuration
    await channel.assertQueue(QUEUE_NAME, {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': EXCHANGE_NAME,
        'x-dead-letter-routing-key': 'dlq'
      }
    });
    console.log(`✅ Created queue: ${QUEUE_NAME}`);

    // Bind queue to exchange
    await channel.bindQueue(QUEUE_NAME, EXCHANGE_NAME, 'analysis.process');
    console.log(`✅ Bound queue ${QUEUE_NAME} to exchange ${EXCHANGE_NAME} with routing key 'analysis.process'`);

    console.log('🎉 RabbitMQ configuration reset successfully!');

  } catch (error) {
    console.error('❌ Error resetting RabbitMQ:', error.message);
    throw error;
  } finally {
    if (channel) {
      await channel.close();
    }
    if (connection) {
      await connection.close();
    }
  }
}

// Run the reset
resetRabbitMQ()
  .then(() => {
    console.log('✅ RabbitMQ reset completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ RabbitMQ reset failed:', error.message);
    process.exit(1);
  });
