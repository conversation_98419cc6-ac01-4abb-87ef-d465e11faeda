/**
 * Simple Flow Testing Script
 * Tests flow: Assessment Service → Analysis Worker → Archive Service (without auth)
 *
 * Prerequisites:
 * - Assessment Service running on port 3003
 * - Analysis Worker running
 * - Archive Service running on port 3002
 * - PostgreSQL database running
 * - RabbitMQ running
 *
 * Usage: node scripts/test-simple-flow.js
 */

require('dotenv').config({ path: '.env.shared' });

const axios = require('axios');
const { Client } = require('pg');

// Configuration
const config = {
  assessmentService: 'http://localhost:3003',
  archiveService: 'http://localhost:3002',
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'atma_db',
    user: process.env.DB_USER || 'atma_user',
    password: process.env.DB_PASSWORD || 'secret-passworrd'
  },
  timeout: 60000 // 1 minute timeout for analysis
};

// Test data
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 55,
    perseverance: 68,
    honesty: 73,
    zest: 66,
    love: 80,
    kindness: 75,
    socialIntelligence: 65,
    teamwork: 60,
    fairness: 70,
    leadership: 67,
    forgiveness: 58,
    humility: 62,
    prudence: 69,
    selfRegulation: 61,
    appreciationOfBeauty: 50,
    gratitude: 72,
    hope: 77,
    humor: 65,
    spirituality: 55
  },
  multipleIntelligences: {
    linguistic: 85,
    logicalMathematical: 90,
    spatial: 75,
    bodilyKinesthetic: 60,
    musical: 55,
    interpersonal: 70,
    intrapersonal: 65,
    naturalistic: 50
  },
  cognitiveStyleIndex: {
    analytic: 80,
    intuitive: 60
  }
};

// Database client
let dbClient;

async function connectToDatabase() {
  console.log('🔌 Connecting to database...');
  dbClient = new Client(config.database);
  await dbClient.connect();
  console.log('✅ Database connected');
}

async function disconnectFromDatabase() {
  if (dbClient) {
    await dbClient.end();
    console.log('🔌 Database connection closed');
  }
}

async function testServiceHealth() {
  console.log('\n🏥 Testing service health...');
  
  try {
    // Test Assessment Service
    const assessmentHealth = await axios.get(`${config.assessmentService}/health`);
    console.log('✅ Assessment Service is healthy:', assessmentHealth.data);
    
    // Test Archive Service
    const archiveHealth = await axios.get(`${config.archiveService}/health`);
    console.log('✅ Archive Service is healthy:', archiveHealth.data);
    
  } catch (error) {
    console.error('❌ Service health check failed:', error.message);
    throw error;
  }
}

async function submitAssessment() {
  console.log('\n📝 Submitting assessment...');
  
  try {
    const response = await axios.post(
      `${config.assessmentService}/test/submit`,
      assessmentData,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Assessment submitted successfully');
    console.log('📄 Response:', response.data);
    
    return response.data.analysisId;
  } catch (error) {
    console.error('❌ Assessment submission failed:', error.response?.data || error.message);
    throw error;
  }
}

async function waitForAnalysis(analysisId) {
  console.log('\n⏳ Waiting for analysis to complete...');
  
  const startTime = Date.now();
  const maxWaitTime = config.timeout;
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      // Check database for completed analysis
      const result = await dbClient.query(
        'SELECT * FROM archive.analysis_results WHERE id = $1',
        [analysisId]
      );
      
      if (result.rows.length > 0) {
        const analysis = result.rows[0];
        console.log('✅ Analysis completed!');
        console.log('📊 Analysis result:', {
          id: analysis.id,
          userId: analysis.user_id,
          status: analysis.status,
          createdAt: analysis.created_at,
          hasPersonaProfile: !!analysis.persona_profile
        });
        
        return analysis;
      }
      
      console.log('⏳ Analysis still processing... waiting 5 seconds');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
    } catch (error) {
      console.error('❌ Error checking analysis status:', error.message);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  throw new Error('Analysis timeout - analysis did not complete within expected time');
}

async function testArchiveRetrieval(analysisId) {
  console.log('\n📚 Testing archive retrieval...');
  
  try {
    const response = await axios.get(
      `${config.archiveService}/results/${analysisId}`,
      {
        headers: {
          'X-Internal-Service-Key': process.env.ARCHIVE_SERVICE_KEY || 'internal_service_secret_key_change_in_production'
        }
      }
    );
    
    console.log('✅ Archive retrieval successful');
    console.log('📄 Retrieved data:', {
      id: response.data.id,
      userId: response.data.userId,
      status: response.data.status,
      hasAssessmentData: !!response.data.assessmentData,
      hasPersonaProfile: !!response.data.personaProfile
    });
    
    return response.data;
  } catch (error) {
    console.error('❌ Archive retrieval failed:', error.response?.data || error.message);
    throw error;
  }
}

async function runTest() {
  console.log('🚀 Starting Simple Flow Test');
  console.log('============================================================');
  
  try {
    // Connect to database
    await connectToDatabase();
    
    // Test service health
    await testServiceHealth();
    
    // Submit assessment
    const analysisId = await submitAssessment();
    
    // Wait for analysis to complete
    const analysis = await waitForAnalysis(analysisId);
    
    // Test archive retrieval
    await testArchiveRetrieval(analysisId);
    
    console.log('\n============================================================');
    console.log('✅ Simple Flow Test PASSED');
    console.log('🎉 All components working correctly!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n============================================================');
    console.log('❌ Simple Flow Test FAILED');
    process.exit(1);
  } finally {
    await disconnectFromDatabase();
  }
}

// Run the test
runTest();
